import { <PERSON>, <PERSON>, Grid, Stack, useTheme, alpha, Typo<PERSON>, <PERSON>conButt<PERSON>, Button, <PERSON><PERSON><PERSON>, Collapse } from "@mui/material"
import { useState } from "react";
import { useTranslation } from "react-i18next";
import FlexBox from "src/@core/components/FlexBox";
import { Iconify } from "src/@core/components/iconify";
import { FacilitiesWorkGroupTaskDetail } from "./facilities-work-group-task-quota";
import NoDataSnackbar from "../_components/no-data-snackbar";
import ReactQuill from "react-quill";
import { facilitiesWorkGroupExecutionType, facilitiesWorkGroupType } from "src/@core/types/facilities";


interface IFacilitiesItemProps {
    data: any,
    onEdit: (data) => void
    onDelete: (data) => void
    onChangeSortOrder: boolean
}

export const FacilitiesWorkGroupTasks = ({ data, onEdit, onDelete, onChangeSortOrder }: IFacilitiesItemProps) => {
    const theme = useTheme();
    const { t } = useTranslation();
    const [openRow, setOpenRow] = useState<any>(null);
    const [facilitiesWorkGroupTask, setFacilitiesWorkGroupTask] = useState<any[]>(data ?? []);

    const renderText = (label, value) => {
        return (
            <Typography variant="caption" color={theme.palette.text.disabled}>{label}: <span style={{ fontWeight: '700' }}>{value}</span></Typography>
        )
    }

    const handleAddFacilitiesTask = (item) => {
        const updatedData = facilitiesWorkGroupTask.map(group => {
            if (group.id === item.id) {
                return {
                    ...group,
                    totalTaskQuota: (group.totalTaskQuota || 0) + 1
                };
            }
            return group;
        });
        setFacilitiesWorkGroupTask(updatedData);
    }

    const handleDeleteFacilitiesTask = (item) => {
        const updatedData = facilitiesWorkGroupTask.map(group => {
            if (group.id === item.id) {
                return {
                    ...group,
                    totalTaskQuota: (group.totalTaskQuota || 0) - 1
                };
            }
            return group;
        });
        setFacilitiesWorkGroupTask(updatedData);
    }

    return (
        <>
            {
                facilitiesWorkGroupTask.length > 0 ?
                    (facilitiesWorkGroupTask.map(item => (
                        <Box sx={{ border: `1px dashed ${openRow === item.id ? theme.palette.primary.dark : theme.palette.action.disabledBackground}`, borderRadius: 1, px: 0.5, py: 1 }}>
                            <Grid container spacing={2}>
                                <Grid item xs={2}>
                                    <Stack gap={'10px'}>
                                        <Chip label={item.taskGroupCode} variant="soft" sx={{ background: (theme) => alpha(theme.palette.grey[500], 0.16), color: theme.palette.text.secondary }} />
                                    </Stack>
                                </Grid>
                                <Grid item xs={4}>
                                    <Stack spacing={1} sx={{
                                        '& .ql-editor': {
                                            padding: 0
                                        }
                                    }}>
                                        <Typography variant="subtitle2" style={{ fontSize: '12px' }}>{item.taskGroupName}</Typography>
                                        <ReactQuill
                                            value={item.description}
                                            readOnly
                                            theme="bubble"
                                            modules={{ toolbar: false }}

                                        />
                                    </Stack>
                                </Grid>
                                <Grid item xs={6}>
                                    <Stack spacing={1} sx={{ width: 1 }}>
                                        <FlexBox justifyContent={'space-around'} sx={{ width: 1 }}>
                                            {renderText(t('page.facilities.type'), facilitiesWorkGroupType.find(f => f.value === item.type)?.name)}
                                            {renderText(t('page.facilities.execution'), facilitiesWorkGroupExecutionType.find(f => f.value === item.executionType)?.name)}
                                            {renderText(t('page.facilities.year'), item.year)}
                                            <Chip
                                                variant="soft"
                                                sx={{
                                                    background: (theme) => alpha(theme.palette.info.main, 0.16),
                                                    color: (theme) => theme.palette.info.dark,
                                                    fontSize: '12px',
                                                    fontWeight: 400,
                                                    height: 'fit-content',
                                                    '& .MuiChip-label': {
                                                        py: 0.5,
                                                        whiteSpace: 'normal',
                                                        wordBreak: 'break-word',
                                                        textOverflow: 'unset',
                                                    }
                                                }}
                                                label={<span>
                                                    {t('page.facilities.content')} <strong>{item.totalTaskQuota}</strong>
                                                </span>}
                                            />
                                        </FlexBox>
                                        <FlexBox justifyContent={'flex-end'} gap={2} sx={{ width: 1 }}>
                                            <IconButton onClick={() => { onEdit(item) }}> <Iconify icon="solar:pen-bold" /></IconButton>
                                            <IconButton> <Iconify icon="solar:copy-bold" /></IconButton>
                                            <IconButton onClick={() => { onDelete(item) }}> <Iconify icon="solar:trash-bin-minimalistic-bold" /></IconButton>
                                            <Button
                                                size="small"
                                                startIcon={<Iconify icon="mdi:chevron-down" />}
                                                variant="contained"
                                                sx={{
                                                    background: (theme) => alpha(theme.palette.grey[500], 0.12),
                                                    color: theme.palette.text.disabled,

                                                }}
                                                onClick={() => { setOpenRow(openRow === item.id ? null : item.id) }}
                                            >
                                                {t('page.lesson.subject.viewMore')}
                                            </Button>
                                        </FlexBox>
                                    </Stack>
                                </Grid>
                            </Grid>
                            <Divider variant="fullWidth" sx={{ opacity: 0.6, borderStyle: 'line', my: 1 }} />
                            <Collapse in={openRow === item.id} timeout="auto" unmountOnExit>
                                <FacilitiesWorkGroupTaskDetail facilitiesWorkGroupId={item.id} facilitiesWorkGroupName={item.taskGroupName} onAddSuccess={() => handleAddFacilitiesTask(item)} onDeleteSuccess={() => handleDeleteFacilitiesTask(item)} onChangeSortOrder={onChangeSortOrder} />
                            </Collapse>
                        </Box>
                    )
                    )
                    ) : (
                        <NoDataSnackbar />
                    )
            }
        </>
    )



}