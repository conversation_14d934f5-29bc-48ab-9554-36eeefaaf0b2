import { yup<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/yup";
import { LoadingButton } from "@mui/lab";
import { Autocomplete, Button, FormControl, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import CustomDrawer from "src/@core/components/drawer/custom-drawer";
import FlexBox from "src/@core/components/FlexBox";
import { FooterDialogAction } from "src/@core/components/Footer";
import FormProvider, { RHFAutocomplete, RHFTextField } from "src/@core/components/hook-form";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import commonService from "src/@core/service/commonService";
import { departmentService } from "src/@core/service/departmentService";
import roomService from "src/@core/service/roomService";
import { AssetTypeRoom } from "src/_mock/faiclities";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import * as Yup from 'yup';

interface props {
    open: boolean;
    onClose: () => void;
    onSuccess: (message: string) => void;
    roomId?: string
    blockId?: string
    floorId?: string;
}

const initValues = {
    roomCode: '',
    roomName: '',
    description: '',
    roomNo: '',
    assetTypeObj: null,
    useDepartmentObj: null,
    manageDepartmentObj: null,
    numberOfSeats: 0,
    floorArea: '0',
    contructionArea: '0',
    valueSettlement: 0,
    originalPrice: 0,
    centralFunding: 0,
    localFunding: 0,
    otherFunding: 0,
    sortOrder: 0,
    roomId: '',//room Code mới
    roomCategoryObj: null
}

export const CreateEditRoom = ({ open, onClose, onSuccess, blockId, floorId, roomId }: props) => {
    const { t } = useTranslation();
    const formMethods = useForm({
        resolver: yupResolver(validateSchema),
        defaultValues: { obj: initValues } as any,
    });
    const { handleSubmit, setValue, trigger, formState, control, watch, getValues } = formMethods;
    const { isSubmitting } = formState as any;
    const [origin, setOrigin] = useState<any>();
    const [departments, setDepartments] = useState<any[]>([]);
    const [roomCategories, setRoomCategories] = useState<any[]>([]);
    const { showError } = useCustomSnackbar();
    useEffect(() => {
        getAllData();
        if (!Boolean(roomId)) return;
        getRoomDataById(roomId);
    }, []);


    const getRoomDataById = async (roomId) => {
        toggleLoading(true);
        const response: any = await roomService.getRoomById(roomId).finally(() => toggleLoading(false));
        if (response) {
            setOrigin(response);
            setValue('obj', { ...response });
            setValue('obj.floorArea', response.floorArea.toString());
        }
    }

    const getAllData = async () => {
        toggleLoading(true);
        const [departmentResponse, roomCategoryResponse]: any = await Promise.all([departmentService.getAllEnableDepartment(), roomService.getAllRoomCategories()]);
        if (departmentResponse) {
            setDepartments(departmentResponse);
        }
        if (roomCategoryResponse) {
            setRoomCategories(roomCategoryResponse);
        }
        toggleLoading(false);
    }

    const handleClose = () => {
        formMethods.reset();
        onClose();
    };

    const onSubmit = handleSubmit(async ({ obj }) => {
        toggleLoading(true);
        const { manageDepartmentObj, assetTypeObj, roomCategoryObj, useDepartmentObj, ...data }: any = obj;
        const payload = {
            ...data,
            blockId: blockId,
            floorId: floorId,
            floorArea: parseFloat(data.floorArea),
            contructionArea: parseFloat(data.contructionArea),
            manageDepartmentCode: manageDepartmentObj?.code,
            manageDepartmentName: manageDepartmentObj?.name,
            useDepartmentCode: useDepartmentObj?.code,
            useDepartmentName: useDepartmentObj?.name,
            assetTypeCode: assetTypeObj?.assetTypeCode,
            assetTypeName: assetTypeObj?.assetTypeName,
            roomCategoryId: roomCategoryObj.id,
            originData: origin,
        };

        let response: any = null;


        if (!roomId) {
            response = await commonService.addRoom(payload).finally(() => toggleLoading(false));
        }
        else {
            // response = await commonService.updateFloor(data.id, payload).finally(() => toggleLoading(false));
            console.log("floorId", floorId);
            toggleLoading(false);
        }
        if (response) {
            onSuccess(roomId ? 'Sửa thành công' : 'Tạo thành công');
            onClose();
            return;
        }
        else {
            showError("Thông tin phòng đã tồn tại");
        }

    });


    return (
        <CustomDrawer
            anchor={'right'}
            title={(!roomId ? t('common.create') : t('common.edit'))}
            disabledClickOutside
            useFormProvider
            open={open}
            onClose={onClose}
        >
            <FormProvider methods={formMethods}>
                <FormControl sx={{ m: 1, width: '500px' }}>
                    <FlexBox gap={'10px'} flexDirection={'column'}>
                        <RHFTextField
                            name="obj.roomName"
                            label="Tên phòng"
                        />

                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.roomId"
                                label="Mã phòng chính"
                            />
                            <RHFTextField
                                name="obj.roomCode"
                                label="Mã phòng phụ"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.floorArea"
                                label="Diện tích"
                            />
                            <RHFTextField
                                name="obj.numberOfSeats"
                                type="number"
                                label="Số chỗ ngồi"
                            />
                        </FlexBox>

                        <FlexBox gap={1}>
                            <RHFAutocomplete
                                name="obj.assetTypeObj"
                                label={t('Loại tài sản')}
                                options={AssetTypeRoom}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.assetTypeName} (${option.assetTypeCode})`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.assetTypeCode}>
                                        {option.assetTypeName} ({option.assetTypeCode})
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.assetTypeCode === value.assetTypeCode}
                            />
                            <RHFAutocomplete
                                name="obj.roomCategoryObj"
                                label={t('Loại phòng')}
                                options={roomCategories}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.categoryName}`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.id}>
                                        {option.categoryName}
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.id === value.id}
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFAutocomplete
                                name="obj.useDepartmentObj"
                                label={t('Được sử dụng bởi khoa')}
                                options={departments}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.name} (${option.code})`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.id}>
                                        {option.name} ({option.code})
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.id === value.id}
                            />
                            <RHFAutocomplete
                                name="obj.manageDepartmentObj"
                                label={t('Được quản lý bởi khoa')}
                                options={departments}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.name} (${option.code})`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.id}>
                                        {option.name} ({option.code})
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.id === value.id}
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.contructionArea"
                                label="Diện tích xây dựng (m²)"
                            />
                            <RHFTextField
                                name="obj.valueSettlement"
                                type="number"
                                label="Giá trị quyết toán (VNĐ)"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.originalPrice"
                                type="number"
                                label="Nguyên giá (VNĐ)"
                            />
                            <RHFTextField
                                name="obj.centralFunding"
                                type="number"
                                label="Nguồn vốn Trung ương (VNĐ)"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.localFunding"
                                type="number"
                                label="Nguồn vốn địa phương (VNĐ)"
                            />
                            <RHFTextField
                                name="obj.otherFunding"
                                type="number"
                                label="Nguồn vốn khác (VNĐ)"
                            />
                        </FlexBox>
                        <RHFTextField
                            multiline
                            rows={4}
                            name="obj.description"
                            label="Mô tả"

                        />


                    </FlexBox>
                </FormControl>
                <FooterDialogAction>
                    <Button variant="outlined" size={'small'} onClick={(e) => handleClose()}>
                        {t('common.cancel')}
                    </Button>
                    <LoadingButton
                        type="button"
                        onClick={onSubmit}
                        size={'small'}
                        loading={isSubmitting}
                        variant="contained"
                    >
                        {t('common.submit')}
                    </LoadingButton>
                </FooterDialogAction>
            </FormProvider>
        </CustomDrawer>
    )
}

const validateSchema = Yup.object().shape({
    obj: Yup.object().shape({
        roomName: Yup.string().required('Yêu cầu nhập'),
        roomCode: Yup.string().required('Yêu cầu nhập'),
        assetTypeObj: Yup.object().required('Yêu cầu nhập'),
        useDepartmentObj: Yup.object().required('Yêu cầu nhập'),
        manageDepartmentObj: Yup.object().required('Yêu cầu nhập'),
        numberOfSeats: Yup.number().required('Yêu cầu nhập'),
        floorArea: Yup.string().required('Yêu cầu nhập'),
        roomId: Yup.string().required('Yêu cầu nhập'), //room Code mới
        roomCategoryObj: Yup.object().required('Yêu cầu nhập')
    }),
});
