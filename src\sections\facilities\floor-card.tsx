import { Accordion, AccordionDetails, AccordionSum<PERSON>y, Button, Stack, Typography } from "@mui/material";
import { memo, useCallback, useEffect, useState } from "react";
import FlexBox from "src/@core/components/FlexBox";
import { Iconify } from "src/@core/components/iconify";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import commonService from "src/@core/service/commonService";
import NoDataSnackbar from "../_components/no-data-snackbar";
import { RoomCard } from "./room-card";
import { CreateEditFloor } from "./create-edit-floor";
import { useBoolean } from "src/hooks/use-boolean";
import { useCustomSnackbar } from "src/hooks/use-snackbar";

interface floorProps {
    blockId: string;
}

export const FloorCard = ({ blockId }: floorProps) => {
    const [floors, setFloors] = useState<any[]>([]);
    const [expandedFloor, setExpandedFloor] = useState<string | null>(null);
    const openCreateEditFloor = useBoolean();
    const { showSuccess } = useCustomSnackbar();
    useEffect(() => {
        getFloorsByBlockId(blockId);
    }, [blockId])

    const getFloorsByBlockId = async (blockId) => {
        toggleLoading(true);
        const response: any = await commonService.getFloors(blockId).finally(() => toggleLoading(false));
        if (response) {
            setFloors(response);
        }
    }
    const handleAccordionChange = (floorId: string) => {
        setExpandedFloor(expandedFloor === floorId ? null : floorId);
    };

    const handleSuccess = (message: string) => {
        showSuccess(message);
        getFloorsByBlockId(blockId);
    }
    return (
        <Stack spacing={1}>
            {openCreateEditFloor.value && <CreateEditFloor open={openCreateEditFloor.value} onClose={openCreateEditFloor.onFalse} blockId={blockId} onSuccess={handleSuccess} />}
            <Button variant="contained" startIcon={<Iconify icon="mdi:plus" />} sx={{ width: 'fit-content' }} onClick={() => { openCreateEditFloor.onTrue() }}>Thêm tầng</Button>
            {floors.length > 0 ? (floors.map(floor => (
                <Accordion
                    key={floor.id}
                    expanded={expandedFloor === floor.id}
                    onChange={() => handleAccordionChange(floor.id)}
                    elevation={0}
                    sx={{
                        border: '1px solid',
                        borderColor: 'divider',
                        '&:before': {
                            display: 'none',
                        },
                        borderRadius: 1,
                        overflow: 'hidden',
                    }}
                >
                    <AccordionSummary
                        expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}
                        sx={{
                            color: 'primary.contrastText',
                            background: 'linear-gradient(153deg,rgba(224, 179, 128, 1) 11%, rgba(230, 207, 230, 1) 82%);'
                        }}
                    >
                        <FlexBox gap={1} alignItems={'center'} >
                            <Iconify icon="material-symbols:stacks-outline-rounded" />
                            <Typography>{floor.floorName}</Typography>
                        </FlexBox>
                    </AccordionSummary>
                    {expandedFloor === floor.id && <AccordionDetails>
                        <RoomCard floorId={floor.id} blockId={blockId} />
                    </AccordionDetails>}
                </Accordion>
            ))) : (<NoDataSnackbar />)}
        </Stack>
    )

}