import { alpha, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>se, <PERSON><PERSON><PERSON>, <PERSON>rid, Icon<PERSON>utton, <PERSON><PERSON>, Typography, useTheme } from "@mui/material";
import { useEffect, useState } from "react";
import FlexBox from "src/@core/components/FlexBox";
import { Iconify } from "src/@core/components/iconify";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import { useBoolean } from "src/hooks/use-boolean";
import { FacilitiesAction } from "./facilities-action";
import React from "react";
import { FacilitiesWorkGroupTasks } from "./facilities-work-group-task";
import facilitiesService from "src/@core/service/facilitiesService";
import { facilitiesWorkGroupType } from "src/@core/types/facilities";
import AddFacilitiesGroupPopup from "./add-facilities-task-group";

interface ILessonSubjectProps {
    facilitiesId: string;
    facilitiesName: string;
    onAddSuccess: () => void;
    onDeleteSuccess: () => void;
}

export const FacilitiesWorkGroups = ({ facilitiesId, facilitiesName, onAddSuccess, onDeleteSuccess }: ILessonSubjectProps) => {
    const [facilitiesTaskGroups, setFacilitiesTaskGroups] = useState<any[]>([]);
    const [origin, setOrigin] = useState<any[]>([]);
    const [currentSelected, setCurrentSelected] = useState<any>(null);
    const [changeSortOrder, setChangeSortOrder] = useState(false);
    const openAddFacilitiesGroup = useBoolean();
    const { showError, showSuccess } = useCustomSnackbar();
    const [openRow, setOpenRow] = useState<any>(null);

    const romanNumerals = [
        "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X",
        "XI", "XII", "XIII", "XIV", "XV", "XVI", "XVII", "XVIII", "XIX", "XX"
    ];

    const renderIndexLabel = (index) => {
        return romanNumerals[index] || "";
    };

    const handleOpenCollapse = (row) => {
        setOpenRow(openRow === row.id ? null : row.id);
    }

    const getFilteredData = (rowValue) => {
        return facilitiesTaskGroups.filter(f => f.type === rowValue);
    }
    const loading = async () => {
        toggleLoading(true);
        const response: any = await facilitiesService.getFacilitiesTaskGroupByFacilitiesId(facilitiesId).finally(() => toggleLoading(false));
        if (response) {
            setOrigin(response);
            setFacilitiesTaskGroups(response);
        }
    }

    const handleSuccess = (message: string) => {
        showSuccess(message);
        loading();
    }

    const handleEditFacilitiesGroup = (row) => {
        setChangeSortOrder(false);
        setCurrentSelected(row);
        openAddFacilitiesGroup.onTrue();
    }

    const handleAddFacilitiesGroup = () => {
        openAddFacilitiesGroup.onTrue();
    }

    const handleDelete = async (item) => {
        const response = await facilitiesService.deleteFacilitiesTaskGroup(item.id);
        if (response) {
            showSuccess("Xóa thành công");
            loading();
            onDeleteSuccess();
        }
        else {
            showError("Có lỗi xảy ra, vui lòng thử lại")
        }
    }


    useEffect(() => {
        facilitiesId && loading();
    }, [facilitiesId])

    return (
        <Stack spacing={2}>
            <FacilitiesAction
                type="group"
                printData={origin}
                onSuccess={handleSuccess}
                openAdd={handleAddFacilitiesGroup}
                actionData={{ lessonId: '' }}
                fileName={`Danh sách công việc của ${facilitiesName}.xlsx`}
            />
            {openAddFacilitiesGroup.value && (
                <AddFacilitiesGroupPopup
                    open={openAddFacilitiesGroup.value}
                    facilitiesId={facilitiesId}
                    data={currentSelected}
                    onSuccess={(messsage) => {
                        showSuccess(messsage);
                        !currentSelected && onAddSuccess();
                        currentSelected && setChangeSortOrder(true);
                        setCurrentSelected(null);
                        loading();
                        openAddFacilitiesGroup.onFalse();
                    }}
                    onClose={() => {
                        openAddFacilitiesGroup.onFalse();
                        setChangeSortOrder(false);
                        setCurrentSelected(null);
                    }}
                />
            )}

            <Stack gap={2}>
                {
                    facilitiesWorkGroupType.map((item, index) => (
                        <React.Fragment key={item.id}>
                            <FlexBox
                                justifyContent={'space-between'}
                                alignItems={'center'}
                                sx={{
                                    width: 1,
                                    px: 1.5, background: (theme) => theme.palette.background.neutral,
                                    borderRadius: 0.5
                                }}>
                                <Typography variant="subtitle2" style={{ fontSize: "14px" }}>{renderIndexLabel(index)}. {item.name}</Typography>
                                <IconButton onClick={() => handleOpenCollapse(item)}>
                                    <Iconify icon="mdi:chevron-down" />
                                </IconButton>
                            </FlexBox>
                            <Collapse in={openRow === item.id} timeout="auto" unmountOnExit>
                                <FacilitiesWorkGroupTasks key={`${item.id} ${new Date().getTime()}`} data={getFilteredData(item.value)} onEdit={handleEditFacilitiesGroup} onDelete={handleDelete} onChangeSortOrder={changeSortOrder} />
                            </Collapse>
                        </React.Fragment>
                    ))
                }
            </Stack>
        </Stack>

    )
}