import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON>, Grid, <PERSON>ack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import roomService from "src/@core/service/roomService";
import NoDataSnackbar from "../_components/no-data-snackbar";
import FlexBox from "src/@core/components/FlexBox";
import { Iconify } from "src/@core/components/iconify";
import { Label } from "src/@core/components/label";
import { useBoolean } from "src/hooks/use-boolean";
import { CreateEditRoom } from "./create-edit-room";
import { useCustomSnackbar } from "src/hooks/use-snackbar";

interface roomProps {
    floorId: string
    blockId: string
}
export const RoomCard = ({ floorId, blockId }: roomProps) => {
    const [rooms, setRooms] = useState<any[]>([]);
    const openCreateEditRoom = useBoolean();
    const { showSuccess } = useCustomSnackbar();
    useEffect(() => {
        getRoomsByFloorId(floorId);
    }, [floorId])

    const getRoomsByFloorId = async (floorId) => {
        toggleLoading(true);
        const response: any = await roomService.getAllRoomsByFloorAndBlock(floorId, blockId).finally(() => toggleLoading(false));
        if (response) {
            setRooms(response);
        }
    }


    const handleSuccess = (message: string) => {
        showSuccess(message);
        getRoomsByFloorId(floorId);
    }

    return (
        <Stack spacing={1}>
            {openCreateEditRoom.value && <CreateEditRoom open={openCreateEditRoom.value} onClose={openCreateEditRoom.onFalse} blockId={blockId} floorId={floorId} onSuccess={handleSuccess} />}
            <Button variant="contained" startIcon={<Iconify icon="mdi:plus" />} sx={{ width: 'fit-content' }} onClick={() => openCreateEditRoom.onTrue()}>Thêm phòng</Button>

            {rooms.length > 0 ? (
                <Grid container spacing={2} sx={{ maxHeight: '60vh', overflowY: 'auto' }}>
                    {
                        rooms.map((room) => (
                            <Grid item xs={12} md={6} key={room.id}>
                                <Card sx={{ p: 1.5, height: 1 }}>
                                    <Stack spacing={1}>
                                        <FlexBox gap={1} justifyContent={'space-between'}>
                                            <FlexBox gap={0.5} >
                                                <Iconify icon="mdi:location" />
                                                <Typography>{room.roomName}</Typography>
                                            </FlexBox>
                                            <Chip label={room.roomCode} variant="soft" color="success" />
                                        </FlexBox>


                                        <FlexBox gap={0.5}>
                                            <Iconify icon="mdi:people-group" />
                                            <Typography>{room.numberOfSeats}</Typography>
                                        </FlexBox>
                                    </Stack>
                                </Card>
                            </Grid>
                        ))
                    }
                </Grid>

            ) :
                (
                    <NoDataSnackbar />
                )



            }
        </Stack>
    )
}