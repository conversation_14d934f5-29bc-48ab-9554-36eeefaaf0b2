import { yupResolver } from "@hookform/resolvers/yup";
import { LoadingButton } from "@mui/lab";
import { Autocomplete, Button, FormControl, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import CustomDrawer from "src/@core/components/drawer/custom-drawer";
import FlexBox from "src/@core/components/FlexBox";
import { FooterDialogAction } from "src/@core/components/Footer";
import FormProvider, { RHFAutocomplete, RHFTextField } from "src/@core/components/hook-form";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import commonService from "src/@core/service/commonService";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import * as Yup from 'yup';

interface props {
    open: boolean;
    onClose: () => void;
    onSuccess: (message: string) => void;
}

const initValues = {
    campusName: '',
    campusName2: '',
    campusSymbol: '',
}

export const CreateEditCampus = ({ open, onClose, onSuccess }: props) => {
    const { t } = useTranslation();
    const formMethods = useForm({
        resolver: yupResolver(validateSchema),
        defaultValues: { obj: initValues } as any,
    });
    const { handleSubmit, setValue, trigger, formState, control, watch, getValues } = formMethods;
    const { isSubmitting } = formState as any;
    const [origin, setOrigin] = useState<any>();
    const { showError } = useCustomSnackbar();




    const handleClose = () => {
        formMethods.reset();
        onClose();
    };

    const onSubmit = handleSubmit(async ({ obj }) => {
        toggleLoading(true);
        const { ...data }: any = obj;
        const payload = {
            ...data,
            originData: origin,
        };

        let response: any = null;

        if (!data.id) {
            response = await commonService.addCampus(payload).finally(() => toggleLoading(false));
        } else {
            response = await commonService.updateFloor(data.id, payload).finally(() => toggleLoading(false));
        }
        if (response) {
            onSuccess(data.id ? 'Sửa thành công' : 'Tạo thành công');
            onClose();
            return;
        }
        else {
            showError("Đã tồn tại cơ sở này! Vui lòng kiểm tra lại");
        }

    });


    return (
        <CustomDrawer
            anchor={'right'}
            title={(t('common.create') + ' ' + t('cơ sở'))}
            disabledClickOutside
            useFormProvider
            open={open}
            onClose={onClose}
        >
            <FormProvider methods={formMethods}>
                <FormControl sx={{ m: 1, width: '400px' }}>
                    <FlexBox gap={'10px'} flexDirection={'column'}>

                        <RHFTextField
                            name="obj.campusName"
                            label="Tên cơ sở"
                        />
                        <RHFTextField
                            name="obj.campusName2"
                            label="Tên phụ"
                        />
                        <RHFTextField
                            name="obj.campusSymbol"
                            label="Ký hiệu"
                            inputProps={{
                                maxLength: 3,
                                error: getValues('obj.campusSymbol').length > 3
                            }}
                            helperText={getValues('obj.campusSymbol').length > 3 ? 'Ký hiệu không được vượt quá 3 ký tự' : ''}
                        />

                    </FlexBox>
                </FormControl>
                <FooterDialogAction>
                    <Button variant="outlined" size={'small'} onClick={(e) => handleClose()}>
                        {t('common.cancel')}
                    </Button>
                    <LoadingButton
                        type="button"
                        onClick={onSubmit}
                        size={'small'}
                        loading={isSubmitting}
                        variant="contained"
                    >
                        {t('common.submit')}
                    </LoadingButton>
                </FooterDialogAction>
            </FormProvider>
        </CustomDrawer>
    )
}

const validateSchema = Yup.object().shape({
    obj: Yup.object().shape({
        campusName: Yup.string().required('Yêu cầu nhập'),
    }),
});
