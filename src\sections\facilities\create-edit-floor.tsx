import { yupResolver } from "@hookform/resolvers/yup";
import { LoadingButton } from "@mui/lab";
import { Autocomplete, Button, FormControl, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import CustomDrawer from "src/@core/components/drawer/custom-drawer";
import FlexBox from "src/@core/components/FlexBox";
import { FooterDialogAction } from "src/@core/components/Footer";
import FormProvider, { RHFAutocomplete, RHFTextField } from "src/@core/components/hook-form";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import commonService from "src/@core/service/commonService";
import * as Yup from 'yup';

interface props {
    open: boolean;
    onClose: () => void;

    onSuccess: (message: string) => void;
    blockId?: string;
}

const initValues = {
    floorName: '',
    floorCode: '',
    description: '',
}

export const CreateEditFloor = ({ open, onClose, onSuccess, blockId }: props) => {
    const { t } = useTranslation();
    const formMethods = useForm({
        resolver: yupResolver(validateSchema),
        defaultValues: { obj: initValues } as any,
    });
    const { handleSubmit, setValue, trigger, formState, control, watch, getValues } = formMethods;
    const { isSubmitting } = formState as any;
    const [origin, setOrigin] = useState<any>();




    const handleClose = () => {
        formMethods.reset();
        onClose();
    };

    const onSubmit = handleSubmit(async ({ obj }) => {
        toggleLoading(true);
        const { ...data }: any = obj;
        const payload = {
            ...data,
            originData: origin,
        };

        let response: any = null;

        if (!data.id) {
            response = await commonService.addFloor(blockId, payload).finally(() => toggleLoading(false));
        } else {
            response = await commonService.updateFloor(data.id, payload).finally(() => toggleLoading(false));
        }
        if (response) {
            formMethods.reset();
            onSuccess(data.id ? 'Sửa thành công' : 'Tạo thành công');
            onClose();
            return;
        }

    });


    return (
        <CustomDrawer
            anchor={'right'}
            title={(t('common.create') + ' ' + t('tầng'))}
            disabledClickOutside
            useFormProvider
            open={open}
            onClose={onClose}
        >
            <FormProvider methods={formMethods}>
                <FormControl sx={{ m: 1, width: '400px' }}>
                    <FlexBox gap={'10px'} flexDirection={'column'}>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.floorName"
                                label="Tên tầng"
                            />
                            <RHFTextField
                                name="obj.floorCode"
                                label="Mã tầng"
                            />
                        </FlexBox>
                        <RHFTextField
                            multiline
                            rows={3}
                            name="obj.description"
                            label="Mô tả"
                        />
                    </FlexBox>
                </FormControl>
                <FooterDialogAction>
                    <Button variant="outlined" size={'small'} onClick={(e) => handleClose()}>
                        {t('common.cancel')}
                    </Button>
                    <LoadingButton
                        type="button"
                        onClick={onSubmit}
                        size={'small'}
                        loading={isSubmitting}
                        variant="contained"
                    >
                        {t('common.submit')}
                    </LoadingButton>
                </FooterDialogAction>
            </FormProvider>
        </CustomDrawer>
    )
}

const validateSchema = Yup.object().shape({
    obj: Yup.object().shape({
        floorName: Yup.string().required('Yêu cầu nhập'),
        floorCode: Yup.string().required('Yêu cầu nhập'),
    }),
});
