"use client"

import { <PERSON><PERSON>, Container, Input<PERSON>dorn<PERSON>, TextField, Typography, alpha, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import CustomBreadcrumbs from "src/@core/components/custom-breadcrumbs";
import { paths } from "src/routes/paths";
import { FacilitiesCard } from "../facilities-card";
import FlexBox from "src/@core/components/FlexBox";
import { Iconify } from "src/@core/components/iconify";
import { escapeRegExp } from "lodash";
import { useEffect, useState } from "react";
import { useBoolean } from "src/hooks/use-boolean";
import AddFacilitiesPopup from "../add-facilities-popup";
import facilitiesService from "src/@core/service/facilitiesService";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import * as ExcelJS from 'exceljs';
import moment from "moment";
import { useSettingsContext } from "src/@core/components/settings";

export const FacilitiesView = () => {
  const { t } = useTranslation();
  const [origins, setOrigins] = useState<any[]>([]);
  const [facilities, setFacilities] = useState<any[]>([]);
  const [currentSelected, setCurrentSelected] = useState<any>(null);
  const theme = useTheme();
  const [keyword, setKeyword] = useState('');
  const openAddFacilities = useBoolean();
  const { showSuccess } = useCustomSnackbar();
  const settings = useSettingsContext();

  useEffect(() => {
    getAllFacilities();
  }, [])

  const getAllFacilities = async () => {
    toggleLoading(true);
    const response: any = await facilitiesService.getFacilities().finally(() => toggleLoading(false));
    if (response) {
      setOrigins(response);
      setFacilities(response);
    }
  }

  const handleFilterName = (event: any) => {
    const searchRegex = new RegExp(escapeRegExp(event.target.value), 'i');
    const filteredRows = origins.filter((row: any) => {
      return Object.keys(row).some((field: any) => {
        return searchRegex.test(row[field]?.toString());
      });
    });
    setFacilities(filteredRows);
    setKeyword(event.target.value);
  };
  const handleDownloadExcel = async () => {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Danh mục CSVC');

    worksheet.columns = [
      { header: 'Tên CSVC', key: 'facilityName', width: 20 },
      { header: 'Mã CSVC', key: 'code', width: 15 },
      { header: 'Số nhóm công việc', key: 'quantityGroup', width: 15 },
      { header: 'Loại CSVC', key: 'type', width: 20 },
      { header: 'Thời gian tạo', key: 'createdAt', width: 20 },
      { header: 'Thời gian cập nhật', key: 'updatedAt', width: 20 },
    ];
    worksheet.properties.defaultRowHeight = 20;

    origins.forEach((row: any, index: number) => {
      worksheet.addRow([
        row.facilityName,
        row.code,
        row.totalTaskGroup,
        row.facilityType,
        moment(row.createdAt).format('DD/MM/YYYY'),
        moment(row.updatedAt).format('DD/MM/YYYY'),
      ]);
    });
    worksheet.getRow(1).font = { bold: true };
    try {
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Danh sách CSVC.xlsx`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Lỗi khi xuất file Excel:', error);
    }
  }

  const handleSuccess = (message: string) => {
    showSuccess(message);
    setCurrentSelected(null);
    getAllFacilities();
  }

  const handleEdit = (item) => {
    setCurrentSelected(item);
    openAddFacilities.onTrue();
  }

  const handleClose = () => {
    openAddFacilities.onFalse();
    setCurrentSelected(null);
  }
  return (
    <Container maxWidth={settings.compactLayout ? false : 'lg'}>
      <CustomBreadcrumbs
        heading={t('page.quota.facilitiesStandard')}
        links={[
          { name: t('main.app'), href: paths.dashboard.root },
          { name: t('page.quota.facilitiesStandard') },
        ]}
      />
      {openAddFacilities.value &&
        <AddFacilitiesPopup
          open={openAddFacilities.value}
          onClose={handleClose}
          data={currentSelected}
          onSuccess={handleSuccess}
        />}
      <FlexBox sx={{ width: 1, marginTop: 4, py: 1 }} justifyContent="space-between" alignItems="center" gap={2}>
        <FlexBox gap={1} alignItems={"center"}>
          <Typography variant="h5">{t('page.facilities.title')}</Typography>
          <Button size="small" variant="contained" sx={{ background: (theme) => alpha(theme.palette.grey[500], 0.08), color: theme.palette.text.primary, whiteSpace: 'nowrap' }} startIcon={<Iconify icon="mdi:plus" sx={{ color: (theme) => theme.palette.primary.dark }} />} onClick={() => { openAddFacilities.onTrue(); }}>{t('page.facilities.createFacilities')}</Button>
          <Button size="small" variant="contained" sx={{ background: (theme) => alpha(theme.palette.grey[500], 0.08), color: theme.palette.text.primary, whiteSpace: 'nowrap' }} startIcon={<Iconify icon="solar:cloud-download-linear" sx={{ color: (theme) => theme.palette.primary.dark }} />} onClick={handleDownloadExcel}>{t('page.facilities.export')}</Button>
        </FlexBox>
        <TextField
          sx={{ width: '300px' }}
          size='small'
          onChange={(e) => {
            handleFilterName(e);
          }}
          value={keyword}
          placeholder={t('common.search')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />
      </FlexBox>
      {facilities && <FacilitiesCard data={facilities} onEdit={handleEdit} />}
    </Container>
  )
}