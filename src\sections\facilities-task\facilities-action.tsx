import { alpha, Button, Typography, useTheme } from "@mui/material"
import { useTranslation } from "react-i18next"
import FlexBox from "src/@core/components/FlexBox"
import { Iconify } from "src/@core/components/iconify"
import * as ExcelJS from 'exceljs'
import { practiceTypes } from "src/@core/types/practiceType"
import moment from "moment"
import { useBoolean } from "src/hooks/use-boolean"
import ImportLessonSubjectPopup from "../lesson/import-lesson-subject-popup"
import ImportLessonPracticePopup from "../lesson/import-lesson-practice-popup"
import { facilitiesWorkGroupExecutionType, facilitiesWorkGroupType } from "src/@core/types/facilities"

interface IActionProps {
    type: string;
    name?: string;
    printData?: any;
    fileName?: string;
    onSuccess: (message: string) => void;
    actionData: {
        lessonId?: string;
        subjectId?: string;
        departmentId?: string;
    }
    openAdd: () => void;
    sx?: any
}

export const FacilitiesAction = ({ type, name, printData, onSuccess, actionData, fileName, openAdd, sx }: IActionProps) => {
    const { t } = useTranslation();
    const openImportLessonSubjectPopup = useBoolean();
    const openImportLessonPracticePopup = useBoolean();
    const renderName = (type: string) => {
        switch (type) {
            case "group":
                return t('Nhóm công việc');
            case "task":
                return t('Công việc thực hiện');

            default:
                return t('');
        }
    }

    const renderAddName = (type: string) => {
        switch (type) {
            case "group":
                return t('Nhóm việc');
            case "task":
                return t('Công việc');

            default:
                return t('');
        }
    }

    function stripTags(html) {
        var temp = document.createElement("div");
        temp.innerHTML = html;
        return temp.textContent || temp.innerText;
    }

    const ExportGroupTask = async () => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Danh sách nhóm công việc');

        worksheet.columns = [
            { header: 'Tên nhóm công việc', key: 'taskGroupName', width: 10 },
            { header: 'Mã nhóm công việc', key: 'taskGroupCode', width: 10 },
            { header: 'Mô tả', key: 'description', width: 30 },
            { header: 'Loại', key: 'type', width: 15 },
            { header: 'Hình thức', key: 'executionType', width: 15 },
            { header: 'Ngày bắt đầu', key: 'fromDate', width: 15 },
            { header: 'Ngày kết thúc', key: 'toDate', width: 15 },
            { header: 'Năm', key: 'year', width: 10 },
            { header: 'Nội dung thực hiện', key: 'totalTaskQuota', width: 10 },
        ];
        worksheet.properties.defaultRowHeight = 20;

        printData.forEach((row: any, index: number) => {
            worksheet.addRow([
                row.taskGroupName,
                row.taskGroupCode,
                stripTags(row.description),
                facilitiesWorkGroupType.find(f => f.value === row.type)?.name,
                facilitiesWorkGroupExecutionType.find(f => f.value === row.executionType)?.name,
                moment(row.fromDate).format('DD/MM/YYYY'),
                moment(row.toDate).format('DD/MM/YYYY'),
                row.year,
                row.totalTaskQuota
            ]);
        });
        worksheet.getRow(1).font = { bold: true };
        try {
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });

            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName ?? '';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Lỗi khi xuất file Excel:', error);
        }
    }

    const ExportTaskQuota = async () => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Danh sách công việc');

        worksheet.columns = [
            { header: 'Tên đầu việc', key: 'taskName', width: 15 },
            { header: 'Mô tả', key: 'description', width: 30 },
            { header: 'Định lượng', key: 'quota', width: 10 },
            { header: 'Chu kỳ ', key: 'period', width: 10 },
            { header: 'Đơn vị tính', key: 'unit', width: 10 },
            { header: 'Chi phí ước tính', key: 'cost', width: 15 },
            { header: 'Tiêu chuẩn đánh giá', key: 'result', width: 30 },
        ];
        worksheet.properties.defaultRowHeight = 20;

        printData.forEach((row: any, index: number) => {
            worksheet.addRow([
                row.taskName,
                stripTags(row.description),
                row.quota,
                row.period,
                row.unit,
                row.cost,
                stripTags(row.result),
            ]);
        });
        worksheet.getRow(1).font = { bold: true };
        try {
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });

            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName ?? '';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Lỗi khi xuất file Excel:', error);
        }
    }

    const ImportSubject = async () => {
        openImportLessonSubjectPopup.onTrue();
    }

    const ImportPractice = async () => {
        openImportLessonPracticePopup.onTrue();
    }

    const handleImport = () => {
        switch (type) {
            case "subject":
                ImportSubject();
                return;

            case "practice":
                ImportPractice();
                return;

            default:
                return;
        }
    }

    const handleExportExcel = async () => {
        switch (type) {
            case "group":
                ExportGroupTask();
                return;

            case "task":
                ExportTaskQuota();
                return;

            default:
                return;
        }
    }
    return (
        <FlexBox alignItems="center" justifyContent="space-between" sx={{ borderRadius: 1, px: 1, background: (theme) => alpha(theme.palette.grey[500], 0.08), ...sx }}>
            <Typography variant="caption" sx={{ flex: 2.5 }}> {t('page.facilities.list')} <b>{renderName(type)}</b> {type === 'group' && t('page.facilities.perform').toLowerCase()} {type === 'task' && t('page.facilities.belongsToWorkGroup').toLowerCase()} {name}</Typography>
            <FlexBox alignItems="center" justifyContent="space-between" sx={{ flex: 1 }}>
                <Button startIcon={<Iconify icon="mdi:plus" />} onClick={openAdd}>{t('common.add')} {renderAddName(type).toLowerCase()}</Button>
                <Button startIcon={<Iconify icon="solar:cloud-upload-linear" />} onClick={handleImport}>{t('common.import')}</Button>
                <Button startIcon={<Iconify icon="solar:cloud-download-linear" />} onClick={handleExportExcel}>{t('common.export')}</Button>
            </FlexBox>
            {openImportLessonSubjectPopup.value && (
                <ImportLessonSubjectPopup
                    lessonId={actionData?.lessonId ?? ''}
                    open={openImportLessonSubjectPopup.value}
                    onSuccess={() => onSuccess("Thêm thành công")}
                    onClose={() => openImportLessonSubjectPopup.onFalse()}
                />
            )}

            {openImportLessonPracticePopup.value && (
                <ImportLessonPracticePopup
                    departmentId={actionData?.departmentId ?? ''}
                    subjectId={actionData?.subjectId ?? ''}
                    open={openImportLessonPracticePopup.value}
                    onSuccess={() => onSuccess("Thêm thành công")}
                    onClose={() => openImportLessonPracticePopup.onFalse()}
                />
            )}
        </FlexBox>
    )
}