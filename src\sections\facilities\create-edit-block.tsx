import { yup<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/yup";
import { Loading<PERSON>utton } from "@mui/lab";
import { Autocomplete, Button, FormControl, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import CustomDrawer from "src/@core/components/drawer/custom-drawer";
import FlexBox from "src/@core/components/FlexBox";
import { FooterDialogAction } from "src/@core/components/Footer";
import FormProvider, { RHFAutocomplete, RHFTextField } from "src/@core/components/hook-form";
import { toggleLoading } from "src/@core/components/loading-screen/loading-overlay";
import commonService from "src/@core/service/commonService";
import { departmentService } from "src/@core/service/departmentService";
import roomService from "src/@core/service/roomService";
import { AssetTypeBlock, AssetTypeRoom } from "src/_mock/faiclities";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import * as Yup from 'yup';

interface props {
    open: boolean;
    onClose: () => void;
    onSuccess: (message: string) => void;
    campusId: string
    blockId?: string
}

const initValues = {
    blockCode: '',
    blockName: '',
    blockName2: '',
    blockNo: '',
    assetTypeObj: null,
    useDepartmentObj: null,
    manageDepartmentObj: null,
    floorArea: '0',
    contructionArea: '0',
    functionCode: '',
    functionName: '',
    valueSettlement: 0,
    originalPrice: 0,
    centralFunding: 0,
    localFunding: 0,
    otherFunding: 0
}

export const CreateEditBlock = ({ open, onClose, onSuccess, campusId, blockId }: props) => {
    const { t } = useTranslation();
    const formMethods = useForm({
        resolver: yupResolver(validateSchema),
        defaultValues: { obj: initValues } as any,
    });
    const { handleSubmit, setValue, trigger, formState, control, watch, getValues } = formMethods;
    const { isSubmitting } = formState as any;
    const [origin, setOrigin] = useState<any>();
    const [departments, setDepartments] = useState<any[]>([]);
    const { showError } = useCustomSnackbar();
    useEffect(() => {
        getAllData();
        if (!Boolean(blockId)) return;
        getBlockDataById(blockId);
    }, []);


    const getBlockDataById = async (blockId) => {
        toggleLoading(true);
        const response: any = await commonService.getBlockById(blockId).finally(() => toggleLoading(false));
        if (response) {
            setOrigin(response);
            setValue('obj', { ...response });
            setValue('obj.floorArea', response.floorArea.toString());
        }
    }

    const getAllData = async () => {
        toggleLoading(true);
        const [departmentResponse]: any = await Promise.all([departmentService.getAllEnableDepartment()]);
        if (departmentResponse) {
            setDepartments(departmentResponse);
        }
        toggleLoading(false);
    }

    const handleClose = () => {
        formMethods.reset();
        onClose();
    };

    const onSubmit = handleSubmit(async ({ obj }) => {
        toggleLoading(true);
        const { manageDepartmentObj, assetTypeObj, useDepartmentObj, ...data }: any = obj;
        const payload = {
            ...data,
            floorArea: parseFloat(data.floorArea),
            contructionArea: parseFloat(data.contructionArea),
            manageDepartmentCode: manageDepartmentObj?.code,
            manageDepartmentName: manageDepartmentObj?.name,
            useDepartmentCode: useDepartmentObj?.code,
            useDepartmentName: useDepartmentObj?.name,
            assetTypeCode: assetTypeObj?.assetTypeCode,
            assetTypeName: assetTypeObj?.assetTypeName,
            originData: origin,
        };

        let response: any = null;


        if (!blockId) {
            response = await commonService.addBlock(campusId, payload).finally(() => toggleLoading(false));
        }
        else {
            // response = await commonService.updateFloor(data.id, payload).finally(() => toggleLoading(false));
            toggleLoading(false);
        }
        if (response) {
            formMethods.reset();
            onSuccess(blockId ? 'Sửa thành công' : 'Tạo thành công');
            onClose();
            return;
        }
        else {
            showError("Mã tòa nhà đã tồn tại");
        }

    });


    return (
        <CustomDrawer
            anchor={'right'}
            title={(!blockId ? t('common.create') : t('common.edit'))}
            disabledClickOutside
            useFormProvider
            open={open}
            onClose={onClose}
        >
            <FormProvider methods={formMethods}>
                <FormControl sx={{ m: 1, width: '500px' }}>
                    <FlexBox gap={'10px'} flexDirection={'column'}>
                        <RHFTextField
                            name="obj.blockName"
                            label="Tên chính tòa nhà"
                        />
                        <RHFTextField
                            name="obj.blockName2"
                            label="Tên phụ tòa nhà"
                        />

                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.blockCode"
                                label="Mã tòa nhà"
                            />
                            <RHFTextField
                                name="obj.blockNo"
                                label="Số hiệu tòa nhà"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.floorArea"
                                label="Diện tích (m²)"
                            />
                            <RHFTextField
                                name="obj.contructionArea"
                                label="Diện tích xây dựng (m²)"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFAutocomplete
                                name="obj.useDepartmentObj"
                                label={t('Được sử dụng bởi khoa')}
                                options={departments}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.name} (${option.code})`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.id}>
                                        {option.name} ({option.code})
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.id === value.id}
                            />
                            <RHFAutocomplete
                                name="obj.manageDepartmentObj"
                                label={t('Được quản lý bởi khoa')}
                                options={departments}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.name} (${option.code})`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.id}>
                                        {option.name} ({option.code})
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.id === value.id}
                            />
                        </FlexBox>

                        <FlexBox gap={1}>
                            <RHFAutocomplete
                                name="obj.assetTypeObj"
                                label={t('Loại tài sản')}
                                options={AssetTypeBlock}
                                fullWidth
                                getOptionLabel={(option: any) => `${option.assetTypeName} (${option.assetTypeCode})`}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.assetTypeCode}>
                                        {option.assetTypeName} ({option.assetTypeCode})
                                    </li>
                                )}
                                isOptionEqualToValue={(option, value) => option.assetTypeCode === value.assetTypeCode}
                            />
                            <RHFTextField
                                name="obj.valueSettlement"
                                type="number"
                                label="Giá trị quyết toán (VNĐ)"
                            />
                        </FlexBox>

                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.functionCode"
                                label="Mã chức năng"
                            />
                            <RHFTextField
                                name="obj.functionName"
                                label="Tên chức năng"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.originalPrice"
                                type="number"
                                label="Nguyên giá (VNĐ)"
                            />
                            <RHFTextField
                                name="obj.centralFunding"
                                type="number"
                                label="Nguồn vốn Trung ương (VNĐ)"
                            />
                        </FlexBox>
                        <FlexBox gap={1}>
                            <RHFTextField
                                name="obj.localFunding"
                                type="number"
                                label="Nguồn vốn địa phương (VNĐ)"
                            />
                            <RHFTextField
                                name="obj.otherFunding"
                                type="number"
                                label="Nguồn vốn khác (VNĐ)"
                            />
                        </FlexBox>
                    </FlexBox>
                </FormControl>
                <FooterDialogAction>
                    <Button variant="outlined" size={'small'} onClick={(e) => handleClose()}>
                        {t('common.cancel')}
                    </Button>
                    <LoadingButton
                        type="button"
                        onClick={onSubmit}
                        size={'small'}
                        loading={isSubmitting}
                        variant="contained"
                    >
                        {t('common.submit')}
                    </LoadingButton>
                </FooterDialogAction>
            </FormProvider>
        </CustomDrawer>
    )
}

const validateSchema = Yup.object().shape({
    obj: Yup.object().shape({
        blockName: Yup.string().required('Yêu cầu nhập'),
        blockCode: Yup.string().required('Yêu cầu nhập'),
        assetTypeObj: Yup.object().required('Yêu cầu nhập'),
        useDepartmentObj: Yup.object().required('Yêu cầu nhập'),
        manageDepartmentObj: Yup.object().required('Yêu cầu nhập'),
        floorArea: Yup.string().required('Yêu cầu nhập'),
        contructionArea: Yup.string().required('Yêu cầu nhập'),
        valueSettlement: Yup.number().required('Yêu cầu nhập'),
        originalPrice: Yup.number().required('Yêu cầu nhập'),
        centralFunding: Yup.number().required('Yêu cầu nhập'),
        localFunding: Yup.number().required('Yêu cầu nhập'),
        otherFunding: Yup.number().required('Yêu cầu nhập')
    }),
});
