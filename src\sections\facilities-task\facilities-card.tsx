"use client"
import { <PERSON>, Checkbox, Chip, <PERSON>lapse, Container, Divider, alpha, Grid, IconButton, <PERSON>ack, Typography, useTheme } from "@mui/material"
import { t } from "i18next"
import FlexBox from "src/@core/components/FlexBox"
import { Iconify } from "src/@core/components/iconify"
import { useEffect, useState } from "react"
import NoDataSnackbar from "../_components/no-data-snackbar"
import React from "react"
import moment from "moment"
import { FacilitiesWorkGroups } from "./facilities-works-group"
import { useBoolean } from "src/hooks/use-boolean"
import { AddFacilityDocumentReference } from "./add-facility-document-reference-popup"

interface ILessonCardProps {
    data?: any
    onEdit: (data) => void;
}

export const FacilitiesCard = ({ data, onEdit }: ILessonCardProps) => {
    const openAddDocumentReference = useBoolean();
    const theme = useTheme();
    const [facilitiesData, setFacilitiesData] = useState<any[]>([]);
    const [currentSelected, setCurrentSelected] = useState<any>(null);
    const [openRow, setOpenRow] = useState<any>(null);

    useEffect(() => {
        setFacilitiesData(data);
    }, [data])
    const renderText = (label, value) => {
        return (
            <Typography variant="caption" color={theme.palette.text.disabled}>{label}: <span style={{ fontWeight: '700' }}>{value}</span></Typography>
        )
    }

    const handleOpenCollapse = (rowId) => {
        setOpenRow(openRow === rowId ? null : rowId)
    }

    const handleAddSuccess = (item) => {
        const updatedData = facilitiesData.map(facility => {
            if (facility.id === item.id) {
                return {
                    ...facility,
                    totalTaskGroup: (facility.totalTaskGroup || 0) + 1
                };
            }
            return facility;
        });
        setFacilitiesData(updatedData);
    }

    const handleDeleteSuccess = (item) => {
        const updatedData = facilitiesData.map(facility => {
            if (facility.id === item.id) {
                return {
                    ...facility,
                    totalTaskGroup: (facility.totalTaskGroup || 0) - 1
                };
            }
            return facility;
        });
        setFacilitiesData(updatedData);
    }
    if (facilitiesData.length > 0) {
        return (
            <Card sx={{ p: 2, mt: 2 }}>
                <Stack spacing={1}>
                    {facilitiesData.map(item => (
                        <React.Fragment key={item.id}>
                            <Grid container spacing={2} alignItems={'center'}>
                                <Grid item xs={1.5}>
                                    <FlexBox alignItems={'center'}>
                                        <Chip size="small" label={item.code} variant="soft" sx={{ background: (theme) => alpha(theme.palette.info.main, 0.16), color: theme.palette.info.dark }} />
                                    </FlexBox>
                                </Grid>
                                <Grid item xs={2}>
                                    <Typography variant="subtitle2">{item.facilityName}</Typography>
                                </Grid>
                                <Grid item xs={1.5}>
                                    <FlexBox gap={0.5} alignItems={'center'}>
                                        <Typography variant="caption" color={theme.palette.text.disabled}>{t('page.facilities.workGroup')}</Typography>
                                        <Chip size="small" label={item.totalTaskGroup ?? 0} variant="soft" sx={{ background: (theme) => alpha(theme.palette.primary.main, 0.16), color: theme.palette.primary.dark }} />
                                    </FlexBox>
                                </Grid>
                                <Grid item xs={1.5}>
                                    {renderText('Type', item.facilityType)}
                                </Grid>
                                <Grid item xs={1.75}>
                                    {renderText(t('page.facilities.createAt'), moment(item.createdAt).format('DD/MM/YYYY'))}
                                </Grid>
                                <Grid item xs={2.25}>
                                    {renderText(t('page.facilities.updateAt'), moment(item.updatedAt).format('DD/MM/YYYY'))}
                                </Grid>
                                <Grid item xs={1.5}>
                                    <FlexBox gap={1} alignItems="center">
                                        <IconButton onClick={() => onEdit(item)}>
                                            <Iconify icon="solar:pen-bold" />
                                        </IconButton>
                                        <IconButton onClick={() => { setCurrentSelected(item); openAddDocumentReference.onTrue(); }}>
                                            <Iconify icon="mdi:file-document" color={theme.palette.warning.main} />
                                        </IconButton>
                                        <IconButton onClick={() => handleOpenCollapse(item.id)}>
                                            <Iconify icon={openRow === item.id ? "mdi:chevron-up" : "mdi:chevron-down"} />
                                        </IconButton>
                                    </FlexBox>
                                </Grid>
                            </Grid>
                            <Divider variant="fullWidth" sx={{ stroke: (theme) => alpha(theme.palette.grey[500], 0.08), borderStyle: 'dashed', my: 2, strokeWidth: '2px' }} />
                            <Collapse in={openRow === item.id} timeout="auto" unmountOnExit>
                                <FacilitiesWorkGroups facilitiesId={item.id} facilitiesName={item.facilityName} onAddSuccess={() => handleAddSuccess(item)} onDeleteSuccess={() => handleDeleteSuccess(item)} />
                            </Collapse>
                        </React.Fragment>
                    ))}
                </Stack>
                {openAddDocumentReference && (
                    <AddFacilityDocumentReference
                        open={openAddDocumentReference.value}
                        onClose={() => openAddDocumentReference.onFalse()}
                        facilityId={currentSelected?.id}
                    />
                )}
            </Card>
        )
    }
    else {
        return (
            <Card sx={{ p: 2, mt: 2 }}>
                <NoDataSnackbar />
            </Card>
        )
    }


}

