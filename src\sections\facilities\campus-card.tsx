import { Accordion, AccordionDetails, Accordion<PERSON><PERSON>mary, alpha, <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardHeader, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material"
import FlexBox from "src/@core/components/FlexBox"
import { Iconify } from "src/@core/components/iconify"
import NoDataSnackbar from "../_components/no-data-snackbar"
import { BlockCard } from "./block-card"
import PRIMARY_COLOR from '../../theme/with-settings/primary-color.json';
import { useState } from "react"

interface props {
    campuses: any[]
}
export const CampusCard = ({ campuses }: props) => {
    const [expandedCampus, setExpandedCampus] = useState<string | null>(null);
    const handleAccordionChange = (campusId: string) => {
        setExpandedCampus(expandedCampus === campusId ? null : campusId);
    };

    return (
        <Stack spacing={2}>
            {campuses.length > 0 ? (
                campuses.map((item) => (
                    <Card key={item.id}>
                        <Accordion
                            key={item.id}
                            expanded={expandedCampus === item.id}
                            onChange={() => handleAccordionChange(item.id)}
                            elevation={0}
                            sx={{
                                border: '1px solid',
                                borderColor: 'divider',
                                '&:before': {
                                    display: 'none',
                                },
                                borderRadius: 1,
                                overflow: 'hidden',
                            }}
                        >
                            <AccordionSummary
                                expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}
                                sx={{ color: 'primary.contrastText', background: 'linear-gradient(141deg,rgba(255, 138, 204, 1) 11%, rgba(250, 69, 32, 1) 100%);', p: 2 }}
                            >
                                <FlexBox gap={1} alignItems={'flex-end'} >
                                    <Iconify icon={"mdi:office-building"} sx={{ width: '32px', height: '32px' }} />
                                    <Typography variant="subtitle2" style={{ fontSize: '1rem' }}>{item.name}</Typography>
                                </FlexBox>
                            </AccordionSummary>
                            {expandedCampus === item.id && <AccordionDetails>
                                <BlockCard
                                    campusId={item.id}
                                />
                            </AccordionDetails>}
                        </Accordion>
                    </Card>
                ))
            ) : (
                <NoDataSnackbar />
            )}
        </Stack>

    )
}