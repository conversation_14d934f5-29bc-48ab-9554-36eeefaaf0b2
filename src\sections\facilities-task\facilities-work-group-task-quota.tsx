import { Box, Chip, GlobalStyles, Icon<PERSON>utton, ListItemText, Typography, alpha } from "@mui/material";
import { FacilitiesAction } from "./facilities-action";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { DataGridView } from "src/@core/components/data-grid";
import NoDataSnackbar from "../_components/no-data-snackbar";
import FlexBox from "src/@core/components/FlexBox";
import { Iconify } from "src/@core/components/iconify";
import ReactQuill from "react-quill";
import { useBoolean } from "src/hooks/use-boolean";
import facilitiesService from "src/@core/service/facilitiesService";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import AddFacilitiesTaskQuotaPopup from "./add-facilities-task-quota";


interface IFacilitiesWorkGroupTaskQuota {
    facilitiesWorkGroupId: string;
    facilitiesWorkGroupName: string;
    onAddSuccess: () => void;
    onDeleteSuccess: () => void;
    onChangeSortOrder?: boolean
}
export const FacilitiesWorkGroupTaskDetail = ({ facilitiesWorkGroupId, facilitiesWorkGroupName, onAddSuccess, onDeleteSuccess, onChangeSortOrder }: IFacilitiesWorkGroupTaskQuota) => {
    const column: any = () => [
        {
            headerName: t('page.facilities.index'),
            field: 'name',
            width: 100,
            renderCell: ({ row }) => {
                return (
                    <Box sx={{ width: '100%', display: "flex", alignItems: 'center', pl: 1, height: '100%' }}>
                        <Typography sx={{ whiteSpace: 'wrap', wordBreak: 'break-word' }} variant="caption">{row.sortOrder}</Typography>
                    </Box>
                )
            }
        },
        {

            renderHeader: ({ row }) => (
                <ListItemText
                    primary={t('page.facilities.taskName')}
                    secondary={t('page.facilities.description')}
                    primaryTypographyProps={{
                        typography: 'caption',
                        textTransform: 'capitalize',
                        fontWeight: 'fontWeightBold',
                    }}
                    secondaryTypographyProps={{
                        component: 'span',
                        typography: 'caption',
                    }} />
            ),
            field: 'description',
            width: 350,
            renderCell: ({ row }) => {
                return (
                    <Box sx={{
                        '& .ql-editor': {
                            padding: 0
                        }
                    }}>
                        <Typography variant="caption" fontWeight={700}>
                            {row.taskName}
                        </Typography>
                        <ReactQuill
                            value={row.description}
                            readOnly={true}
                            theme="bubble"
                            modules={{ toolbar: false }}
                        />
                    </Box>
                )
            }
        },
        {
            headerName: t('page.facilities.quota'),
            field: 'quota',
            width: 120,
            headerAlign: 'center',
            renderCell: ({ row }) => {
                return (
                    <Box sx={{ width: '100%', display: "flex", alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <Typography sx={{ whiteSpace: 'wrap', wordBreak: 'break-word' }} variant="caption">{row.quota}</Typography>
                    </Box>
                )
            }
        },
        {
            headerName: t('page.facilities.period'),
            field: 'period',
            width: 120,
            headerAlign: 'center',
            renderCell: ({ row }) => {
                return (
                    <Box sx={{ width: '100%', display: "flex", alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <Typography sx={{ whiteSpace: 'wrap', wordBreak: 'break-word' }} variant="caption">{row.period}</Typography>
                    </Box>
                )
            }
        },
        {
            headerName: t('page.facilities.unit'),
            field: 'unit',
            minWidth: 120,
            headerAlign: 'center',
            renderCell: ({ row }) => {
                return (
                    <Box sx={{ width: '100%', display: "flex", alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <Typography sx={{ whiteSpace: 'wrap', wordBreak: 'break-word' }} variant="caption">{row.unit}</Typography>
                    </Box>
                )
            }
        },
        {
            headerName: t('page.facilities.cost'),
            field: 'cost',
            width: 120,
            headerAlign: 'center',
            renderCell: ({ row }) => {
                return (
                    <Box sx={{ width: '100%', display: "flex", alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <Typography sx={{ whiteSpace: 'wrap', wordBreak: 'break-word' }} variant="caption">{row.cost}</Typography>
                    </Box>
                )
            }
        },

        {
            headerName: t('page.facilities.result'),
            field: 'result',
            width: 150,
            headerAlign: 'center',
            renderCell: ({ row }) => {
                return (
                    <Box sx={{
                        width: '100%', display: "flex", alignItems: 'center', justifyContent: 'center', height: '100%', '& .ql-editor': {
                            padding: 0
                        }
                    }}>
                        <ReactQuill
                            value={row.result}
                            readOnly={true}
                            theme="bubble"
                            modules={{ toolbar: false }}
                        />
                    </Box>
                )
            }
        },

        {
            field: '#',
            headerName: t('page.facilities.settings'),
            minWidth: 300,
            headerAlign: 'center',
            renderCell: ({ row }) => {
                return (
                    <FlexBox gap={2} alignItems="center" height={1} justifyContent="center">
                        <IconButton
                            onClick={() => { }}
                        >
                            <Iconify icon="mdi:eye" />
                        </IconButton>
                        <IconButton
                            onClick={() => { handleEdit(row) }}
                        >
                            <Iconify icon="solar:pen-bold" />
                        </IconButton>

                        <IconButton onClick={() => { }}>
                            <Iconify icon="solar:copy-bold" />
                        </IconButton>

                        <IconButton onClick={() => { handleDelete(row) }}>
                            <Iconify icon="solar:trash-bin-minimalistic-bold" />
                        </IconButton>
                    </FlexBox>
                );
            },
        },
    ];
    const { t } = useTranslation();
    const [data, setData] = useState<any[]>([]);
    const [currentSelected, setCurrentSelected] = useState<any>(null);
    const openAddTaskQuota = useBoolean();
    const loading = useBoolean();
    const { showError, showSuccess } = useCustomSnackbar();

    useEffect(() => {
        onChangeSortOrder && load();
    }, [onChangeSortOrder])

    useEffect(() => {
        facilitiesWorkGroupId && load();
    }, [facilitiesWorkGroupId])

    const load = async () => {
        loading.onTrue();
        const response: any = await facilitiesService.getFacilitiesTaskQuotaByTaskGroupId(facilitiesWorkGroupId).finally(() => loading.onFalse());
        if (response) {
            setData(response);
        }
    }

    const handleEdit = (item) => {
        setCurrentSelected(item);
        openAddTaskQuota.onTrue();
    }
    const handleDelete = async (item) => {
        loading.onTrue();
        const response: any = await facilitiesService.deleteFacilitiesTaskQuota(item.id).finally(() => loading.onFalse());
        if (response) {
            showSuccess("Xóa thành công");
            load();
            onDeleteSuccess();
        }
        else {
            showError("Có lỗi xảy ra, vui lòng thử lại")
        }
    }
    return (
        <Box sx={{
            px: 1,
            minWidth: 0,
            width: '100%',
            overflowX: 'auto' // Thêm dòng này
        }}>
            {openAddTaskQuota.value && (
                <AddFacilitiesTaskQuotaPopup
                    open={openAddTaskQuota.value}
                    facilityTaskGroupId={facilitiesWorkGroupId}
                    data={currentSelected}
                    onSuccess={(message) => {
                        showSuccess(message);
                        load();
                        !currentSelected && onAddSuccess();
                        setCurrentSelected(null);
                    }}
                    onClose={() => {
                        openAddTaskQuota.onFalse();
                        setCurrentSelected(null);
                    }}
                />
            )}
            <GlobalStyles
                styles={(theme) => ({
                    '.even-row': {
                        backgroundColor: theme.palette.grey[100],
                    },
                    '.odd-row': {
                        backgroundColor: theme.palette.background.default,
                    },
                    '.MuiDataGrid-row': {
                        paddingTop: '4px',
                        paddingBottom: '4px',
                    }
                })}
            />
            <FacilitiesAction
                type="task"
                name={facilitiesWorkGroupName}
                sx={{ mb: 1 }}
                printData={data}
                onSuccess={(message) => { showSuccess(message) }}
                actionData={{ subjectId: 'subjectId' }}
                openAdd={() => { openAddTaskQuota.onTrue() }}
                fileName={`Danh sách công việc của ${facilitiesWorkGroupName}.xlsx`}
            />
            {data.length > 0 ? (
                <DataGridView
                    rowId={'id'}
                    loading={loading.value}
                    rows={data}
                    columns={column()}
                    height={'auto'}
                    hideFooter
                    getRowClassName={(params) =>
                        params.indexRelativeToCurrentPage % 2 === 0 ? 'even-row' : 'odd-row'
                    }
                    columnHeaderHeight={40}
                    getRowHeight={() => 'auto'}

                />
            ) : (
                <NoDataSnackbar />
            )}

        </Box>
    )
}