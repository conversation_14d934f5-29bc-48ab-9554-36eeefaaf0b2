import { Accordion, AccordionDetails, AccordionSummary, Box, Button, Card, Stack, Typography } from "@mui/material";
import { memo, useCallback, useEffect, useState } from "react";
import { Iconify } from "src/@core/components/iconify";
import commonService from "src/@core/service/commonService";
import NoDataSnackbar from "../_components/no-data-snackbar";
import FlexBox from "src/@core/components/FlexBox";
import { FloorCard } from "./floor-card";
import { useBoolean } from "src/hooks/use-boolean";
import { CreateEditFloor } from "./create-edit-floor";
import { useCustomSnackbar } from "src/hooks/use-snackbar";
import { CreateEditBlock } from "./create-edit-block";

interface blockProps {
    campusId: string;
}


export const BlockCard = ({ campusId }: blockProps) => {
    const [blocks, setBlocks] = useState<any[]>([]);
    const [expandedBlock, setExpandedBlock] = useState<string | null>(null);
    const openCreateEditBlock = useBoolean();
    const { showSuccess } = useCustomSnackbar();
    useEffect(() => {
        getBlocksByCampusId(campusId);
    }, [campusId])

    const getBlocksByCampusId = async (campusId) => {
        const response: any = await commonService.getBlocks(campusId);
        if (response) {
            setBlocks(response);
        }

    }

    const handleAccordionChange = (blockId: string) => {
        setExpandedBlock(expandedBlock === blockId ? null : blockId);
    };

    const handleSuccess = (message: string) => {
        showSuccess(message);
        getBlocksByCampusId(campusId);
    }


    return (
        <Stack spacing={1} >
            <Button variant="contained" startIcon={<Iconify icon="mdi:plus" />} sx={{ width: 'fit-content' }} onClick={() => openCreateEditBlock.onTrue()}>Thêm tòa nhà</Button>
            {openCreateEditBlock && <CreateEditBlock open={openCreateEditBlock.value} onClose={openCreateEditBlock.onFalse} campusId={campusId} onSuccess={handleSuccess} />}
            {blocks.length > 0 ? (blocks.map(block => (
                <>
                    <Accordion
                        key={block.id}
                        expanded={expandedBlock === block.id}
                        onChange={() => handleAccordionChange(block.id)}
                        elevation={0}
                        sx={{
                            border: '1px solid',
                            borderColor: 'divider',
                            '&:before': {
                                display: 'none',
                            },
                            borderRadius: 1,
                            overflow: 'hidden',
                        }}
                    >
                        <AccordionSummary
                            expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}
                            sx={{
                                color: 'primary.contrastText',
                                background: 'linear-gradient(153deg,rgba(148, 242, 198, 1) 11%, rgba(230, 161, 224, 1) 100%);',
                            }}
                        >
                            <FlexBox gap={1} alignItems={'center'} >
                                <Iconify icon="mdi:house-variant" />
                                <Typography>{block.blockName}</Typography>
                            </FlexBox>
                        </AccordionSummary>
                        {expandedBlock === block.id && <AccordionDetails>
                            <FloorCard blockId={block.id} />
                        </AccordionDetails>}
                    </Accordion>

                </>

            ))) : (
                <NoDataSnackbar />
            )}
        </Stack>
    )
}